import { useState, useEffect, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { PhoneInput } from "@/components/ui/phone-input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Heart, Share2, ArrowLeft, Gift } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import type { PledgeInsert, WeddingProfile } from "@/types/app";
import { 
  validatePhoneNumber, 
  validateAmount,
  ValidationError
} from "@/types/app";
import PledgeThankYou from "@/components/PledgeThankYou";

interface PledgeData {
  brideName: string;
  groomName: string;
  weddingDate: string;
  venue: string;
  treasurerName: string;
  treasurerPhone: string;
  specialMessage: string;
  theme: string;
}

const PledgeCard = () => {
  const { pledgeId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [pledgeData, setPledgeData] = useState<PledgeData | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [notFound, setNotFound] = useState(false);

  const [pledgeForm, setPledgeForm] = useState({
    guestName: "",
    phoneNumber: "",
    email: "",
    pledgeAmount: "",
    paymentDate: "",
    message: ""
  });

  const [showPledgeForm, setShowPledgeForm] = useState(false);
  const [showThankYou, setShowThankYou] = useState(false);
  const [submittedPledge, setSubmittedPledge] = useState<{
    guestName: string;
    amount: number;
  } | null>(null);

  const themes = {
    sunset: "from-orange-400 to-pink-500",
    royal: "from-purple-500 to-blue-600",
    garden: "from-green-400 to-teal-500",
    golden: "from-yellow-400 to-orange-500"
  };

  const fetchPledgeData = useCallback(async () => {
    try {
      if (pledgeId === 'demo') {
        setPledgeData({
          brideName: "Sarah",
          groomName: "David",
          weddingDate: "2024-06-15",
          venue: "Kampala Serena Hotel",
          treasurerName: "Grace Nakato",
          treasurerPhone: "+256 777 123 456",
          specialMessage: "Join us in celebrating our special day with your loving support and contributions.",
          theme: "sunset"
        });
        setLoading(false);
        return;
      }

      console.log('Fetching profile for pledge ID:', pledgeId);
      
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', pledgeId)
        .maybeSingle();

      console.log('Profile fetch result:', { profile, profileError });

      if (profileError) {
        console.error('Error fetching profile:', profileError);
        throw profileError;
      }

      if (!profile) {
        console.error('No profile found for ID:', pledgeId);
        setNotFound(true);
        setLoading(false);
        return;
      }

      // Check if profile is public (unless it's demo)
      if (pledgeId !== 'demo' && !profile.is_public) {
        console.error('Profile is not public for ID:', pledgeId);
        setNotFound(true);
        setLoading(false);
        return;
      }

      console.log('Profile fetched successfully:', profile);
      setPledgeData({
        brideName: profile.bride_name,
        groomName: profile.groom_name,
        weddingDate: profile.wedding_date,
        venue: profile.venue || '',
        treasurerName: profile.treasurer_name,
        treasurerPhone: profile.treasurer_phone || '',
        specialMessage: profile.special_message || "Join us in celebrating our special day with your loving support and contributions.",
        theme: profile.theme || "sunset"
      });
    } catch (error: unknown) {
      console.error('Error in fetchPledgeData:', error);
      setNotFound(true);
    } finally {
      setLoading(false);
    }
  }, [pledgeId]);

  useEffect(() => {
    fetchPledgeData();
  }, [fetchPledgeData]);

  const handleShare = async () => {
    const url = window.location.href;
    const text = `Join ${pledgeData?.brideName} & ${pledgeData?.groomName}'s wedding celebration! Make your pledge here:`;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Wedding Pledge Card',
          text: text,
          url: url
        });
      } catch (err) {
        console.log('Error sharing:', err);
      }
    } else {
      navigator.clipboard.writeText(`${text} ${url}`);
      toast({
        title: "Link Copied! 📋",
        description: "Share this link with family and friends via WhatsApp, SMS, or email.",
      });
    }
  };

  const validatePledgeData = (data: {
    guestName: string;
    phoneNumber: string;
    pledgeAmount: string;
    paymentDate?: string;
  }) => {
    const errors: string[] = [];

    if (!data.guestName.trim()) {
      errors.push('Guest name is required');
    }

    if (data.phoneNumber) {
      const phoneValidation = validatePhoneNumber(data.phoneNumber);
      if (!phoneValidation.isValid) {
        errors.push(...phoneValidation.errors);
      }
    }

    const amount = Number(data.pledgeAmount);
    if (isNaN(amount)) {
      errors.push('Invalid pledge amount');
    } else {
      const amountValidation = validateAmount(amount);
      if (!amountValidation.isValid) {
        errors.push(...amountValidation.errors);
      }
    }

    if (errors.length > 0) {
      throw new ValidationError(errors.join(', '));
    }
  };

  const handlePledgeSubmit = async () => {
    if (!pledgeForm.guestName || !pledgeForm.pledgeAmount) {
      toast({
        title: "Missing Information",
        description: "Please fill in your name and pledge amount.",
        variant: "destructive"
      });
      return;
    }

    setSubmitting(true);
    try {
      // Validate data before sending to Supabase
      validatePledgeData(pledgeForm);

      const pledgeData: PledgeInsert = {
        user_id: pledgeId!,
        guest_name: pledgeForm.guestName,
        guest_phone: pledgeForm.phoneNumber || null,
        amount_pledged: Number(pledgeForm.pledgeAmount),
        amount_paid: 0,
        payment_status: 'pending',
        pledge_date: new Date().toISOString(),
        payment_date: pledgeForm.paymentDate || null,
        notes: pledgeForm.message || null,
      };

      const { error } = await supabase
        .from('pledges')
        .insert(pledgeData);

      if (error) throw error;

      // Store submitted pledge info for thank you message
      setSubmittedPledge({
        guestName: pledgeForm.guestName,
        amount: Number(pledgeForm.pledgeAmount)
      });

      // Reset form and show thank you
      setPledgeForm({
        guestName: "",
        phoneNumber: "",
        email: "",
        pledgeAmount: "",
        paymentDate: "",
        message: ""
      });
      setShowPledgeForm(false);
      setShowThankYou(true);
    } catch (error: unknown) {
      console.error('Error submitting pledge:', error);
      toast({
        title: "Error",
        description: "Failed to submit pledge. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleResetToForm = () => {
    setShowThankYou(false);
    setSubmittedPledge(null);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-100 via-pink-50 to-purple-100 py-8 flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
            <p>Loading pledge card...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (notFound || !pledgeData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 py-8 flex items-center justify-center px-4">
        <Card className="w-96 shadow-2xl border-0 rounded-2xl">
          <CardContent className="p-8 text-center">
            <Heart className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h2 className="text-xl font-bold mb-4">Pledge Card Not Found</h2>
            <p className="text-gray-600 mb-6">This pledge card doesn't exist or may have been removed.</p>
            <Button onClick={() => navigate('/')} className="mb-4">
              Visit PledgeForLove Uganda
            </Button>
            <div className="text-xs text-gray-400">
              Create your own digital wedding card
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const weddingDate = pledgeData.weddingDate ? new Date(pledgeData.weddingDate) : null;
  const formattedDate = weddingDate?.toLocaleDateString('en-GB', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 py-4 px-4">
      <div className="container mx-auto max-w-2xl">
        {/* Subtle branding link */}
        <div className="text-center mb-6">
          <button
            onClick={() => navigate('/')}
            className="text-sm text-gray-500 hover:text-gray-700 transition-colors duration-200 flex items-center justify-center gap-1 mx-auto"
          >
            <Heart className="h-4 w-4" />
            <span>Powered by PledgeForLove Uganda</span>
          </button>
        </div>

        {/* Share button - moved to be more prominent */}
        <div className="flex justify-center mb-6">
          <Button
            onClick={handleShare}
            variant="outline"
            className="border-purple-300 text-purple-700 hover:bg-purple-50"
          >
            <Share2 className="h-4 w-4 mr-2" />
            Share This Card
          </Button>
        </div>

        {showThankYou && submittedPledge ? (
          <PledgeThankYou
            pledgeData={{
              brideName: pledgeData.brideName,
              groomName: pledgeData.groomName,
              theme: pledgeData.theme
            }}
            guestName={submittedPledge.guestName}
            pledgeAmount={submittedPledge.amount}
            onReset={handleResetToForm}
            onShare={handleShare}
          />
        ) : (
          <Card className="bg-white shadow-2xl mb-8 overflow-hidden border-0 rounded-2xl">
            <div className={`bg-gradient-to-br ${themes[pledgeData.theme]} p-8 text-white text-center`}>
              <Heart className="h-16 w-16 mx-auto mb-6 opacity-80" />
              <h1 className="text-3xl md:text-4xl font-bold mb-4">
                {pledgeData.brideName} & {pledgeData.groomName}
              </h1>
              <p className="text-xl opacity-90 mb-2">are getting married!</p>
              {formattedDate && <p className="text-lg opacity-80 mb-4">{formattedDate}</p>}
              {pledgeData.venue && (
                <p className="opacity-80 mb-6">📍 {pledgeData.venue}</p>
              )}
              <div className="bg-white/20 backdrop-blur-sm rounded-lg p-6">
                <p className="text-lg leading-relaxed">
                  {pledgeData.specialMessage}
                </p>
              </div>
            </div>
            
            <CardContent className="p-8">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-gray-800 mb-4">Support Our Special Day</h2>
                <p className="text-gray-600 mb-6">
                  Your contribution will help make our wedding celebration memorable. 
                  Every pledge is deeply appreciated and will be used to create beautiful memories.
                </p>
                
                {!showPledgeForm ? (
                  <Button 
                    onClick={() => setShowPledgeForm(true)}
                    className={`bg-gradient-to-r ${themes[pledgeData.theme]} text-white px-8 py-3 text-lg hover:opacity-90`}
                    size="lg"
                  >
                    <Gift className="mr-2 h-5 w-5" />
                    Make a Pledge
                  </Button>
                ) : (
                  <div className="space-y-6 sm:space-y-8 text-left">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                      <div>
                        <Label htmlFor="guestName">Your Name *</Label>
                        <Input
                          id="guestName"
                          value={pledgeForm.guestName}
                          onChange={(e) => setPledgeForm(prev => ({...prev, guestName: e.target.value}))}
                          placeholder="Enter your full name"
                          className="mt-1"
                          disabled={submitting}
                          autoComplete="name"
                        />
                      </div>
                      <div>
                        <Label htmlFor="phoneNumber">Phone Number</Label>
                        <PhoneInput
                          value={pledgeForm.phoneNumber}
                          onChange={(formatted, normalized) => setPledgeForm(prev => ({...prev, phoneNumber: normalized}))}
                          showValidation={true}
                          className="mt-1"
                          disabled={submitting}
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="email">Email (Optional)</Label>
                      <Input
                        id="email"
                        type="email"
                        value={pledgeForm.email}
                        onChange={(e) => setPledgeForm(prev => ({...prev, email: e.target.value}))}
                        placeholder="<EMAIL>"
                        className="mt-1"
                        disabled={submitting}
                      />
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                      <div>
                        <Label htmlFor="pledgeAmount">Pledge Amount (UGX) *</Label>
                        <Input
                          id="pledgeAmount"
                          type="number"
                          inputMode="numeric"
                          value={pledgeForm.pledgeAmount}
                          onChange={(e) => setPledgeForm(prev => ({...prev, pledgeAmount: e.target.value}))}
                          placeholder="100000"
                          className="mt-1"
                          disabled={submitting}
                        />
                      </div>
                      <div>
                        <Label htmlFor="paymentDate">Preferred Payment Date</Label>
                        <Input
                          id="paymentDate"
                          type="date"
                          value={pledgeForm.paymentDate}
                          onChange={(e) => setPledgeForm(prev => ({...prev, paymentDate: e.target.value}))}
                          className="mt-1"
                          disabled={submitting}
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="message">Message for the Couple (Optional)</Label>
                      <Textarea
                        id="message"
                        value={pledgeForm.message}
                        onChange={(e) => setPledgeForm(prev => ({...prev, message: e.target.value}))}
                        placeholder="Your best wishes for the happy couple..."
                        className="mt-1"
                        rows={3}
                        disabled={submitting}
                      />
                    </div>

                    <div className="flex gap-4">
                      <Button 
                        onClick={handlePledgeSubmit}
                        className={`bg-gradient-to-r ${themes[pledgeData.theme]} text-white flex-1`}
                        disabled={submitting}
                      >
                        {submitting ? 'Submitting...' : 'Submit Pledge'}
                      </Button>
                      <Button 
                        onClick={() => setShowPledgeForm(false)}
                        variant="outline"
                        disabled={submitting}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}
              </div>

              {pledgeData.treasurerName && (
                <div className="border-t pt-6 text-center">
                  <h3 className="font-semibold text-gray-800 mb-2">Contact Information</h3>
                  <p className="text-gray-600">Treasurer: {pledgeData.treasurerName}</p>
                  {pledgeData.treasurerPhone && (
                    <p className="text-gray-600">Phone: {pledgeData.treasurerPhone}</p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Subtle footer link */}
        <div className="text-center mt-8 pb-4">
          <button
            onClick={() => navigate('/')}
            className="text-xs text-gray-400 hover:text-gray-600 transition-colors duration-200"
          >
            Create your own digital wedding card at PledgeForLove.ug
          </button>
        </div>
      </div>
    </div>
  );
};

export default PledgeCard;
