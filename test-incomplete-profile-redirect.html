<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Incomplete Profile Redirect Test Helper</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: #f5f5f5;
            border-left: 4px solid #007cba;
        }
        .expected {
            background: #e8f5e8;
            border-left: 4px solid #28a745;
            padding: 10px;
            margin: 10px 0;
        }
        .warning {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 10px;
            margin: 10px 0;
        }
        .button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .button:hover {
            background: #005a8b;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin: 5px;
        }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .pending { background: #d1ecf1; color: #0c5460; }
        .checklist {
            list-style-type: none;
            padding: 0;
        }
        .checklist li {
            margin: 5px 0;
            padding: 5px;
            background: #f8f9fa;
            border-radius: 3px;
        }
        .checklist input[type="checkbox"] {
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <h1>🧪 Incomplete Profile Redirect Test Helper</h1>
    <p>This helper guide will walk you through testing the incomplete profile redirect functionality in the Love Pledge Uganda application.</p>
    
    <div class="warning">
        <strong>Prerequisites:</strong>
        <ul>
            <li>Application running at <a href="http://localhost:8081/" target="_blank">http://localhost:8081/</a></li>
            <li>Browser developer tools open (F12)</li>
            <li>Clear browser storage for clean test</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>🚀 Quick Start</h3>
        <a href="http://localhost:8081/" target="_blank" class="button">Open Application</a>
        <button onclick="clearStorage()" class="button">Clear Browser Storage</button>
        <button onclick="openDevTools()" class="button">Open Dev Tools</button>
    </div>

    <div class="test-section">
        <h3>📋 Test Scenario 1: New User Signup and Redirect</h3>
        <div class="step">
            <strong>Step 1:</strong> Navigate to <a href="http://localhost:8081/" target="_blank">http://localhost:8081/</a>
        </div>
        <div class="step">
            <strong>Step 2:</strong> Click "Get Started" or authentication button
        </div>
        <div class="step">
            <strong>Step 3:</strong> Switch to "Sign Up" tab in the modal
        </div>
        <div class="step">
            <strong>Step 4:</strong> Fill signup form with new email/password (use unique email)
        </div>
        <div class="step">
            <strong>Step 5:</strong> Submit signup form and observe redirect
        </div>
        <div class="expected">
            <strong>Expected:</strong> User redirected to /profile-setup page with empty form
        </div>
        <ul class="checklist">
            <li><input type="checkbox"> Modal closes after signup</li>
            <li><input type="checkbox"> URL changes to /profile-setup</li>
            <li><input type="checkbox"> Profile setup form is displayed</li>
            <li><input type="checkbox"> Form fields are empty</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>🔒 Test Scenario 2: Protected Route Access</h3>
        <div class="step">
            <strong>Step 1:</strong> While on profile setup page, manually navigate to protected routes
        </div>
        <div class="step">
            <strong>Test URLs:</strong>
            <ul>
                <li><a href="http://localhost:8081/dashboard" target="_blank">Dashboard</a></li>
                <li><a href="http://localhost:8081/profile" target="_blank">Profile</a></li>
            </ul>
        </div>
        <div class="expected">
            <strong>Expected:</strong> All protected routes redirect back to /profile-setup
        </div>
        <ul class="checklist">
            <li><input type="checkbox"> Dashboard redirects to profile-setup</li>
            <li><input type="checkbox"> Profile page redirects to profile-setup</li>
            <li><input type="checkbox"> Console shows redirect logs</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>✅ Test Scenario 3: Profile Setup Completion</h3>
        <div class="step">
            <strong>Step 1:</strong> Fill required fields on profile setup page:
            <ul>
                <li>Bride Name: "Test Bride"</li>
                <li>Groom Name: "Test Groom"</li>
                <li>Wedding Date: (future date)</li>
                <li>Treasurer Name: "Test Treasurer"</li>
            </ul>
        </div>
        <div class="step">
            <strong>Step 2:</strong> Submit the profile setup form
        </div>
        <div class="step">
            <strong>Step 3:</strong> Test access to protected routes after completion
        </div>
        <div class="expected">
            <strong>Expected:</strong> Profile created, redirected to dashboard, full access granted
        </div>
        <ul class="checklist">
            <li><input type="checkbox"> Form submits successfully</li>
            <li><input type="checkbox"> Redirected to dashboard</li>
            <li><input type="checkbox"> Dashboard loads without redirect</li>
            <li><input type="checkbox"> Can access profile page</li>
            <li><input type="checkbox"> Profile data displays correctly</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>🛡️ Test Scenario 4: Profile Setup Access Prevention</h3>
        <div class="step">
            <strong>Step 1:</strong> After completing profile setup, manually navigate to:
            <a href="http://localhost:8081/profile-setup" target="_blank">/profile-setup</a>
        </div>
        <div class="expected">
            <strong>Expected:</strong> Redirected to dashboard (cannot access profile setup when profile exists)
        </div>
        <ul class="checklist">
            <li><input type="checkbox"> Profile-setup redirects to dashboard</li>
            <li><input type="checkbox"> Console shows appropriate logs</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>🔄 Test Scenario 5: State Persistence</h3>
        <div class="step">
            <strong>Step 1:</strong> Refresh the browser page
        </div>
        <div class="step">
            <strong>Step 2:</strong> Close and reopen browser tab
        </div>
        <div class="expected">
            <strong>Expected:</strong> Authentication state and redirect behavior persists
        </div>
        <ul class="checklist">
            <li><input type="checkbox"> User remains authenticated after refresh</li>
            <li><input type="checkbox"> Appropriate redirects still work</li>
            <li><input type="checkbox"> Profile state is maintained</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>🐛 Console Messages to Monitor</h3>
        <div class="step">
            Watch for these key console messages:
            <ul>
                <li><code>ProtectedRoute: No user found, redirecting to auth</code></li>
                <li><code>ProtectedRoute: User has no profile, allowing access to profile-setup</code></li>
                <li><code>ProtectedRoute: User has profile, redirecting to dashboard</code></li>
                <li><code>Fetching profile for user: [user-id]</code></li>
                <li><code>Profile fetched successfully</code> or <code>No profile found for user</code></li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h3>🎯 Test Results Summary</h3>
        <div id="results">
            <p>Complete the test scenarios above and check off items as you verify them.</p>
        </div>
    </div>

    <script>
        function clearStorage() {
            if (confirm('This will clear all browser storage (localStorage, sessionStorage, cookies). Continue?')) {
                localStorage.clear();
                sessionStorage.clear();
                // Clear cookies for localhost
                document.cookie.split(";").forEach(function(c) { 
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
                });
                alert('Browser storage cleared! You can now test with a clean state.');
            }
        }

        function openDevTools() {
            alert('Press F12 to open Developer Tools, then go to the Console tab to monitor logs.');
        }

        // Auto-update results based on checkboxes
        document.addEventListener('change', function(e) {
            if (e.target.type === 'checkbox') {
                updateResults();
            }
        });

        function updateResults() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            const total = checkboxes.length;
            const checked = document.querySelectorAll('input[type="checkbox"]:checked').length;
            const percentage = Math.round((checked / total) * 100);
            
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `
                <p><strong>Progress:</strong> ${checked}/${total} items completed (${percentage}%)</p>
                <div style="background: #e9ecef; border-radius: 10px; height: 20px; overflow: hidden;">
                    <div style="background: #28a745; height: 100%; width: ${percentage}%; transition: width 0.3s;"></div>
                </div>
            `;
        }

        // Initialize results
        updateResults();
    </script>
</body>
</html>
