# Incomplete Profile Redirect Test Plan

## Overview
This test plan covers the user redirect functionality for incomplete profiles in the Love Pledge Uganda application. The app implements a two-stage onboarding flow where signup creates a user account only, then redirects to a separate profile setup page.

## Test Environment
- **Application URL**: http://localhost:8081/
- **Test Browser**: Any modern browser
- **Prerequisites**: Application running locally

## Key Components to Test

### 1. ProtectedRoute Logic
- **File**: `src/components/ProtectedRoute.tsx`
- **Key Logic**: Routes with `requireProfile={true}` redirect to `/profile-setup` if no profile exists

### 2. Authentication Flow
- **File**: `src/components/auth/AuthModal.tsx`
- **Key Logic**: After signup, navigates to `/dashboard` which triggers redirect to `/profile-setup`

### 3. Profile Setup Page
- **File**: `src/pages/ProfileSetup.tsx`
- **Key Logic**: Creates profile and redirects to dashboard upon completion

## Test Scenarios

### Scenario 1: New User Signup and Redirect
**Objective**: Verify that new users are redirected to profile setup after signup

**Steps**:
1. Open http://localhost:8081/
2. Click "Get Started" or navigate to authentication
3. Switch to "Sign Up" tab in the modal
4. Fill in signup form with new email/password
5. Submit signup form
6. Observe redirect behavior

**Expected Results**:
- User account is created successfully
- Modal closes after signup
- User is redirected to `/profile-setup` page
- Profile setup form is displayed with empty fields

### Scenario 2: Incomplete Profile Access to Protected Routes
**Objective**: Verify that users without profiles cannot access protected routes

**Steps**:
1. Complete Scenario 1 (be on profile setup page)
2. Manually navigate to `/dashboard` in browser address bar
3. Manually navigate to `/profile` in browser address bar
4. Try accessing other protected routes

**Expected Results**:
- All attempts to access protected routes redirect back to `/profile-setup`
- No access to dashboard or profile pages without completing profile

### Scenario 3: Profile Setup Completion and Access
**Objective**: Verify that completing profile setup grants access to protected routes

**Steps**:
1. Be on `/profile-setup` page (from previous scenarios)
2. Fill in required fields:
   - Bride Name
   - Groom Name
   - Wedding Date (future date)
   - Treasurer Name
3. Optionally fill other fields (venue, phone, etc.)
4. Submit the profile setup form
5. Try accessing `/dashboard` and `/profile` routes

**Expected Results**:
- Profile is created successfully
- User is redirected to `/dashboard`
- Dashboard loads without redirect
- User can access `/profile` page
- Profile data is displayed correctly

### Scenario 4: Profile Setup Validation
**Objective**: Verify form validation prevents incomplete submissions

**Steps**:
1. Be on `/profile-setup` page
2. Try submitting with missing required fields
3. Try submitting with past wedding date
4. Try submitting with invalid data

**Expected Results**:
- Form validation prevents submission
- Error messages are displayed
- User remains on profile setup page
- No profile is created with invalid data

### Scenario 5: Existing User with Profile
**Objective**: Verify that users with complete profiles cannot access profile setup

**Steps**:
1. Complete profile setup (Scenario 3)
2. Manually navigate to `/profile-setup` in browser address bar
3. Verify redirect behavior

**Expected Results**:
- User is redirected from `/profile-setup` to `/dashboard`
- Cannot access profile setup page when profile exists

### Scenario 6: Authentication State Persistence
**Objective**: Verify redirect behavior persists across browser sessions

**Steps**:
1. Complete signup but not profile setup
2. Refresh the browser
3. Navigate to protected routes
4. Close and reopen browser
5. Navigate to the application

**Expected Results**:
- User remains authenticated after refresh
- Still redirected to profile setup if profile incomplete
- Redirect behavior persists across browser sessions

## Manual Testing Checklist

### Pre-Test Setup
- [ ] Application is running on http://localhost:8081/
- [ ] Browser developer tools are open for debugging
- [ ] Clear browser storage/cookies for clean test

### Test Execution
- [ ] Scenario 1: New User Signup and Redirect
- [ ] Scenario 2: Incomplete Profile Access to Protected Routes  
- [ ] Scenario 3: Profile Setup Completion and Access
- [ ] Scenario 4: Profile Setup Validation
- [ ] Scenario 5: Existing User with Profile
- [ ] Scenario 6: Authentication State Persistence

### Debugging Information
Monitor browser console for:
- Authentication state changes
- Profile fetch operations
- Route navigation logs
- Error messages

### Key Console Messages to Watch
- `ProtectedRoute: No user found, redirecting to auth`
- `ProtectedRoute: User has no profile, allowing access to profile-setup`
- `ProtectedRoute: User has profile, redirecting to dashboard`
- `Fetching profile for user: [user-id]`
- `Profile fetched successfully` or `No profile found for user`

## Expected Flow Summary

1. **New User**: Home → Auth Modal → Signup → Dashboard (redirects to) → Profile Setup
2. **Incomplete Profile**: Any Protected Route → Profile Setup
3. **Complete Profile**: Profile Setup → Dashboard → Full App Access
4. **Existing User**: Profile Setup → Dashboard (redirect)

## Common Issues to Watch For

1. **Redirect Loops**: User bouncing between routes
2. **Authentication Delays**: Profile fetch timing issues
3. **Form Validation**: Required field enforcement
4. **State Management**: Profile state not updating after creation
5. **Route Protection**: Unauthorized access to protected routes

## Success Criteria

✅ New users are properly redirected to profile setup after signup
✅ Users without profiles cannot access protected routes
✅ Profile setup form validates required fields
✅ Completing profile setup grants access to all features
✅ Users with profiles cannot access profile setup page
✅ Authentication and redirect state persists across sessions
