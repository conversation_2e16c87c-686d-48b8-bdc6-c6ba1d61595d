import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Heart, Settings, Palette, Eye, EyeOff } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import LoadingSpinner from "@/components/ui/loading-spinner";
import { useSEO } from "@/utils/seo";

const ProfileSetup = () => {
  const { user, profile, refreshProfile } = useAuth();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    bride_name: "",
    groom_name: "",
    wedding_date: "",
    venue: "",
    treasurer_name: "",
    treasurer_phone: "",
    email: "",
    theme: "sunset",
    special_message: "Join us in celebrating our special day with your loving support and contributions.",
    is_public: false,
  });

  const navigate = useNavigate();
  const { toast } = useToast();

  // SEO optimization for profile setup page
  useSEO('profile-setup');

  const themes = {
    sunset: "Sunset Romance",
    royal: "Royal Elegance",
    garden: "Garden Fresh",
    golden: "Golden Glow"
  };

  useEffect(() => {
    // Populate form with existing profile data, clearing placeholder values
    if (profile) {
      const isPlaceholder = (value: string, placeholder: string) =>
        value === placeholder || !value?.trim();

      setFormData({
        bride_name: isPlaceholder(profile.bride_name, 'Bride Name') ? '' : profile.bride_name,
        groom_name: isPlaceholder(profile.groom_name, 'Groom Name') ? '' : profile.groom_name,
        treasurer_name: isPlaceholder(profile.treasurer_name, 'Treasurer Name') ? '' : profile.treasurer_name,
        wedding_date: profile.wedding_date || '',
        venue: profile.venue || '',
        treasurer_phone: profile.treasurer_phone || '',
        email: profile.email || user?.email || '',
        theme: profile.theme || 'sunset',
        special_message: profile.special_message || 'Join us in celebrating our special day with your loving support and contributions.',
        is_public: profile.is_public || false,
      });
    } else if (user?.email) {
      // Fallback: just set email if no profile exists yet
      setFormData(prev => ({ ...prev, email: user.email }));
    }
  }, [user, profile]);

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = () => {
    const requiredFields = ['bride_name', 'groom_name', 'wedding_date', 'treasurer_name'];
    const missingFields = requiredFields.filter(field => !formData[field as keyof typeof formData]);
    
    if (missingFields.length > 0) {
      toast({
        title: "Missing Required Fields",
        description: `Please fill in: ${missingFields.join(', ').replace(/_/g, ' ')}`,
        variant: "destructive",
      });
      return false;
    }

    // Validate wedding date is not in the past
    const weddingDate = new Date(formData.wedding_date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (weddingDate < today) {
      toast({
        title: "Invalid Wedding Date",
        description: "Wedding date cannot be in the past.",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);

    try {
      // Validate current session
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        toast({
          title: "Authentication Required",
          description: "Please sign in again to continue.",
          variant: "destructive",
        });
        navigate('/auth');
        return;
      }

      console.log('Updating profile for user:', session.user.id);

      const profileData = {
        id: session.user.id,
        bride_name: formData.bride_name,
        groom_name: formData.groom_name,
        wedding_date: formData.wedding_date,
        venue: formData.venue || null,
        treasurer_name: formData.treasurer_name,
        treasurer_phone: formData.treasurer_phone || null,
        email: formData.email,
        theme: formData.theme,
        special_message: formData.special_message,
        is_public: formData.is_public,
        updated_at: new Date().toISOString(),
      };

      // Use upsert to handle both new profiles and updating existing placeholder profiles
      const { error: upsertError } = await supabase
        .from('profiles')
        .upsert(profileData, { onConflict: 'id' });

      if (upsertError) {
        console.error('Error updating profile:', upsertError);
        throw upsertError;
      }

      // Verify profile was updated
      const { data: profile, error: fetchError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (fetchError || !profile) {
        console.error('Error verifying profile:', fetchError);
        throw new Error('Failed to verify profile update');
      }

      console.log('Profile updated successfully:', profile);

      // Refresh profile in auth context
      await refreshProfile();

      toast({
        title: "Profile Setup Complete! 🎉",
        description: "Your wedding profile has been completed successfully.",
      });

      // Redirect to dashboard
      navigate('/dashboard');
    } catch (error) {
      console.error('Error creating profile:', error);
      if (error instanceof Error) {
        toast({
          title: "Error",
          description: error.message,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Error",
          description: "Failed to create profile. Please try again.",
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handlePreview = () => {
    navigate('/demo-pledge');
  };

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner text="Loading..." />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8 text-center">
        <div className="flex justify-center mb-4">
          <Heart className="h-12 w-12 text-pink-500" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Complete Your Wedding Profile
        </h1>
        <p className="text-gray-600">
          Set up your wedding details and customize your pledge card
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Wedding Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Wedding Details
            </CardTitle>
            <CardDescription>
              Basic information about your wedding
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="bride_name">Bride's Name *</Label>
                <Input
                  id="bride_name"
                  value={formData.bride_name}
                  onChange={(e) => handleInputChange('bride_name', e.target.value)}
                  placeholder="Enter bride's name"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="groom_name">Groom's Name *</Label>
                <Input
                  id="groom_name"
                  value={formData.groom_name}
                  onChange={(e) => handleInputChange('groom_name', e.target.value)}
                  placeholder="Enter groom's name"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="wedding_date">Wedding Date *</Label>
                <Input
                  id="wedding_date"
                  type="date"
                  value={formData.wedding_date}
                  onChange={(e) => handleInputChange('wedding_date', e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="venue">Venue</Label>
                <Input
                  id="venue"
                  value={formData.venue}
                  onChange={(e) => handleInputChange('venue', e.target.value)}
                  placeholder="Wedding venue (optional)"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="treasurer_name">Treasurer Name *</Label>
                <Input
                  id="treasurer_name"
                  value={formData.treasurer_name}
                  onChange={(e) => handleInputChange('treasurer_name', e.target.value)}
                  placeholder="Person managing pledges"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="treasurer_phone">Treasurer Phone</Label>
                <Input
                  id="treasurer_phone"
                  value={formData.treasurer_phone}
                  onChange={(e) => handleInputChange('treasurer_phone', e.target.value)}
                  placeholder="Contact number (optional)"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email Address *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="Your email address"
                required
              />
            </div>
          </CardContent>
        </Card>

        {/* Card Customization */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="h-5 w-5" />
              Card Customization
            </CardTitle>
            <CardDescription>
              Customize the appearance and message of your pledge card
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="theme">Card Theme</Label>
              <Select
                value={formData.theme}
                onValueChange={(value) => handleInputChange('theme', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a theme" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(themes).map(([key, label]) => (
                    <SelectItem key={key} value={key}>{label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="special_message">Special Message for Guests</Label>
              <Textarea
                id="special_message"
                value={formData.special_message}
                onChange={(e) => handleInputChange('special_message', e.target.value)}
                placeholder="Your message to guests..."
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Privacy Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {formData.is_public ? <Eye className="h-5 w-5" /> : <EyeOff className="h-5 w-5" />}
              Privacy Settings
            </CardTitle>
            <CardDescription>
              Control who can access your pledge card
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="is_public">Make Profile Public</Label>
                <p className="text-sm text-gray-500">
                  Allow guests to access your pledge card without authentication
                </p>
              </div>
              <Switch
                id="is_public"
                checked={formData.is_public}
                onCheckedChange={(checked) => handleInputChange('is_public', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4">
          <Button type="submit" disabled={loading} className="flex-1">
            {loading ? (
              <>
                <LoadingSpinner className="mr-2 h-4 w-4" />
                Creating Profile...
              </>
            ) : (
              'Complete Profile Setup'
            )}
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={handlePreview}
            disabled={loading}
            className="flex-1 sm:flex-initial"
          >
            <Eye className="h-4 w-4 mr-2" />
            Preview Card
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ProfileSetup;
