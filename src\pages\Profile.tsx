
import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PhoneInput } from "@/components/ui/phone-input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from "@/components/ui/form";
import { Switch } from "@/components/ui/switch";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { supabase } from "@/integrations/supabase/client";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { Trash2, AlertTriangle, Eye, EyeOff, ArrowLeft } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import type { WeddingProfileUpdate } from "@/types/app";
import { 
  handleSupabaseError, 
  validatePhoneNumber, 
  validateWeddingDate,
  isAuthError,
  isValidationError,
  isDatabaseError,
  ValidationError,
  AuthError
} from "@/types/app";
import { useAuth } from "@/contexts/AuthContext";

const profileSchema = z.object({
  treasurerName: z.string().min(2, "Treasurer name is required"),
  brideName: z.string().min(2, "Bride name is required"),
  groomName: z.string().min(2, "Groom name is required"),
  treasurerPhone: z.string().optional(),
  weddingDate: z.string().optional(),
  venue: z.string().optional(),
  isPublic: z.boolean(),
});

const Profile = () => {
  const { user, profile, refreshProfile } = useAuth();
  const [loading, setLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();

  const form = useForm<z.infer<typeof profileSchema>>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      treasurerName: "",
      brideName: "",
      groomName: "",
      treasurerPhone: "",
      weddingDate: "",
      venue: "",
      isPublic: false,
    },
  });

  useEffect(() => {
    // Populate form when profile data is available
    if (profile) {
      form.reset({
        treasurerName: profile.treasurer_name || "",
        brideName: profile.bride_name || "",
        groomName: profile.groom_name || "",
        treasurerPhone: profile.treasurer_phone || "",
        weddingDate: profile.wedding_date || "",
        venue: profile.venue || "",
        isPublic: profile.is_public || false,
      });
    }
  }, [profile, form]);

  const validateProfileData = (values: z.infer<typeof profileSchema>) => {
    const errors: string[] = [];

    if (values.treasurerPhone) {
      const phoneValidation = validatePhoneNumber(values.treasurerPhone);
      if (!phoneValidation.isValid) {
        errors.push(...phoneValidation.errors);
      }
    }

    if (values.weddingDate) {
      const dateValidation = validateWeddingDate(values.weddingDate);
      if (!dateValidation.isValid) {
        errors.push(...dateValidation.errors);
      }
    }

    if (errors.length > 0) {
      throw new ValidationError(errors.join(', '));
    }
  };

  const onSubmit = async (values: z.infer<typeof profileSchema>) => {
    setLoading(true);
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) throw new AuthError("Not authenticated");

      // Validate data before sending to Supabase
      validateProfileData(values);

      const updateData: WeddingProfileUpdate = {
        treasurer_name: values.treasurerName,
        bride_name: values.brideName,
        groom_name: values.groomName,
        treasurer_phone: values.treasurerPhone || null,
        wedding_date: values.weddingDate || null,
        venue: values.venue || null,
        is_public: values.isPublic,
        updated_at: new Date().toISOString(),
      };

      const { error } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', session.user.id);

      if (error) throw error;

      toast({
        title: "Success!",
        description: values.isPublic 
          ? "Profile updated and made public. Your pledge card is now accessible to guests!"
          : "Profile updated successfully.",
      });
    } catch (error: unknown) {
      if (isAuthError(error)) {
        toast({
          title: "Authentication Error",
          description: error.message,
          variant: "destructive",
        });
      } else if (isValidationError(error)) {
        toast({
          title: "Validation Error",
          description: error.message,
          variant: "destructive",
        });
      } else if (isDatabaseError(error)) {
        toast({
          title: "Database Error",
          description: error.message,
          variant: "destructive",
        });
      } else {
        handleSupabaseError(error);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAccount = async () => {
    setDeleteLoading(true);
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) throw new AuthError("Not authenticated");

      // Delete all pledges first (cascade should handle this, but being explicit)
      const { error: pledgesError } = await supabase
        .from('pledges')
        .delete()
        .eq('user_id', session.user.id);

      if (pledgesError) throw pledgesError;

      // Delete profile (this will cascade to user due to foreign key)
      const { error: profileError } = await supabase
        .from('profiles')
        .delete()
        .eq('id', session.user.id);

      if (profileError) throw profileError;

      // Sign out and redirect
      await supabase.auth.signOut();
      
      toast({
        title: "Account Deleted",
        description: "Your account and all data have been permanently deleted.",
      });
      
      navigate('/');
    } catch (error: unknown) {
      if (isAuthError(error)) {
        toast({
          title: "Authentication Error",
          description: error.message,
          variant: "destructive",
        });
      } else if (isDatabaseError(error)) {
        toast({
          title: "Database Error",
          description: error.message,
          variant: "destructive",
        });
      } else {
        handleSupabaseError(error);
      }
    } finally {
      setDeleteLoading(false);
    }
  };

  const copyPledgeLink = () => {
    if (user?.id) {
      const pledgeUrl = `${window.location.origin}/pledge/${user.id}`;
      navigator.clipboard.writeText(pledgeUrl);
      toast({
        title: "Link Copied!",
        description: "Share this link with your guests so they can make pledges.",
      });
    }
  };

  const isPublic = form.watch('isPublic');

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      {/* Back to Dashboard Link */}
      <div className="mb-6">
        <Button
          variant="ghost"
          onClick={() => navigate('/dashboard')}
          className="flex items-center gap-2 text-gray-600 hover:text-gray-900 p-0"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Dashboard
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Profile Settings</CardTitle>
          <CardDescription>
            Update your wedding and treasurer information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="treasurerName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Treasurer Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="brideName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bride's Name</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="groomName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Groom's Name</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="treasurerPhone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number</FormLabel>
                    <FormControl>
                      <PhoneInput
                        value={field.value || ''}
                        onChange={(formatted, normalized) => field.onChange(normalized)}
                        showValidation={true}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Profile Completion Indicator */}
              {(!form.watch('weddingDate') || !form.watch('venue')) && (
                <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-amber-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-sm font-medium text-amber-800">Complete Your Profile</h3>
                      <p className="text-sm text-amber-700 mt-1">
                        Add your wedding date and venue to create a more complete pledge card for your guests.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <FormField
                control={form.control}
                name="weddingDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Wedding Date</FormLabel>
                    <FormControl>
                      <Input {...field} type="date" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="venue"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Venue</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Wedding venue" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isPublic"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base flex items-center gap-2">
                        {field.value ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                        Make Pledge Card Public
                      </FormLabel>
                      <FormDescription className="text-sm text-gray-600">
                        When enabled, guests can access your pledge card without signing in.
                        {field.value && " Your pledge card is now public!"}
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {isPublic && user?.id && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-green-800">Pledge Card Link</h4>
                      <p className="text-sm text-green-600">Share this link with your guests</p>
                    </div>
                    <Button
                      type="button"
                      onClick={copyPledgeLink}
                      variant="outline"
                      size="sm"
                      className="border-green-300 text-green-700 hover:bg-green-100"
                    >
                      Copy Link
                    </Button>
                  </div>
                </div>
              )}

              <Button type="submit" disabled={loading}>
                {loading ? "Updating..." : "Update Profile"}
              </Button>
            </form>
          </Form>

          <div className="mt-8 pt-8 border-t">
            <div className="flex items-center gap-3 mb-4">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <h3 className="text-lg font-semibold text-red-700">Danger Zone</h3>
            </div>
            
            <p className="text-sm text-gray-600 mb-4">
              Permanently delete your account and all associated data. This action cannot be undone.
            </p>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" className="flex items-center gap-2">
                  <Trash2 className="h-4 w-4" />
                  Delete Account
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete your account
                    and all pledge data associated with your wedding.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDeleteAccount}
                    disabled={deleteLoading}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    {deleteLoading ? "Deleting..." : "Yes, delete my account"}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Profile;
